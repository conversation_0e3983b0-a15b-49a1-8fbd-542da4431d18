{"version": 3, "file": "getFolderStructure.test.js", "sourceRoot": "", "sources": ["../../../src/utils/getFolderStructure.test.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,uDAAuD;AACvD,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,SAAS,EAAQ,MAAM,QAAQ,CAAC;AAC/E,OAAO,UAAU,MAAM,aAAa,CAAC;AACrC,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AAGzB,OAAO,EAAE,kBAAkB,EAAE,MAAM,yBAAyB,CAAC;AAC7D,OAAO,KAAK,QAAQ,MAAM,eAAe,CAAC;AAC1C,OAAO,EAAE,oBAAoB,EAAE,MAAM,qCAAqC,CAAC;AAE3E,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;IACvC,MAAM,QAAQ,GAAG,CAAC,MAAM,cAAc,EAAE,CAAoB,CAAC;IAC7D,OAAO;QACL,GAAG,QAAQ;QACX,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC;QAC5B,0FAA0F;KAC3F,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACvB,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACd,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AAEzB,0DAA0D;AAC1D,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAE7B,8DAA8D;AAC9D,MAAM,YAAY,GAAG,CAAC,IAAY,EAAE,IAAoB,EAAY,EAAE,CAAC,CAAC;IACtE,IAAI;IACJ,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,KAAK,MAAM;IAC7B,WAAW,EAAE,GAAG,EAAE,CAAC,IAAI,KAAK,KAAK;IACjC,aAAa,EAAE,GAAG,EAAE,CAAC,KAAK;IAC1B,iBAAiB,EAAE,GAAG,EAAE,CAAC,KAAK;IAC9B,cAAc,EAAE,GAAG,EAAE,CAAC,KAAK;IAC3B,MAAM,EAAE,GAAG,EAAE,CAAC,KAAK;IACnB,QAAQ,EAAE,GAAG,EAAE,CAAC,KAAK;IACrB,IAAI,EAAE,EAAE;IACR,UAAU,EAAE,EAAE;CACf,CAAC,CAAC;AAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;IAClC,UAAU,CAAC,GAAG,EAAE;QACd,EAAE,CAAC,aAAa,EAAE,CAAC;QAEnB,8DAA8D;QAC9D,uFAAuF;QACvF,+FAA+F;QAC/F,2DAA2D;QAC1D,IAAI,CAAC,OAAgB,CAAC,kBAAkB,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;QAEhE,+EAA+E;QAC9E,UAAU,CAAC,OAAgB,CAAC,kBAAkB,CAC7C,KAAK,EAAE,OAA8B,EAAE,EAAE;YACvC,uDAAuD;YACvD,sEAAsE;YACtE,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC1D,IAAI,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC;gBACpC,OAAO,eAAe,CAAC,cAAc,CAAC,CAAC;YACzC,CAAC;YACD,MAAM,MAAM,CAAC,MAAM,CACjB,IAAI,KAAK,CACP,+CAA+C,cAAc,GAAG,CACjE,EACD,EAAE,IAAI,EAAE,QAAQ,EAAE,CACnB,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,EAAE,CAAC,eAAe,EAAE,CAAC,CAAC,sFAAsF;IAC9G,CAAC,CAAC,CAAC;IAEH,MAAM,eAAe,GAA+B;QAClD,WAAW,EAAE;YACX,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC;YACjC,YAAY,CAAC,YAAY,EAAE,KAAK,CAAC;YACjC,YAAY,CAAC,aAAa,EAAE,KAAK,CAAC;YAClC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;YACnC,YAAY,CAAC,cAAc,EAAE,KAAK,CAAC;SACpC;QACD,sBAAsB,EAAE;YACtB,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC;YACjC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC;YACjC,YAAY,CAAC,YAAY,EAAE,KAAK,CAAC;SAClC;QACD,iCAAiC,EAAE,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QACtE,uBAAuB,EAAE,EAAE;QAC3B,wBAAwB,EAAE,CAAC,YAAY,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QAC9D,2BAA2B,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAC/D,YAAY,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,CACtC;QACD,uBAAuB,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAC1D,YAAY,CAAC,UAAU,CAAC,EAAE,EAAE,KAAK,CAAC,CACnC;QACD,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACtC,CAAC,gCAAgC,CAAC,EAAE,CAAC,EAAE;gBACrC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC;aAClC;SACF,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;QAClD,uBAAuB,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QACxD,8BAA8B,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC/D,qCAAqC,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QACtE,4CAA4C,EAAE;YAC5C,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC;SACjC;KACF,CAAC;IAEF,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;QACpD,MAAM,SAAS,GAAG,MAAM,kBAAkB,CAAC,sBAAsB,CAAC,CAAC;QACnE,MAAM,QAAQ,GAAG;;;;;;;;CAQpB,CAAC,IAAI,EAAE,CAAC;QACL,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;QAC7C,MAAM,SAAS,GAAG,MAAM,kBAAkB,CAAC,uBAAuB,CAAC,CAAC;QACpE,MAAM,QAAQ,GAAG;;;;CAIpB,CAAC,IAAI,EAAE,CAAC;QACL,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6DAA6D,EAAE,KAAK,IAAI,EAAE;QAC3E,MAAM,SAAS,GAAG,MAAM,kBAAkB,CAAC,WAAW,CAAC,CAAC;QACxD,MAAM,QAAQ,GAAG;;;;;;;;;;;;;CAapB,CAAC,IAAI,EAAE,CAAC;QACL,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;QACxE,MAAM,SAAS,GAAG,MAAM,kBAAkB,CAAC,WAAW,EAAE;YACtD,cAAc,EAAE,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;SACxD,CAAC,CAAC;QACH,MAAM,QAAQ,GAAG;;;;;;;;;CASpB,CAAC,IAAI,EAAE,CAAC;QACL,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;QACzD,MAAM,SAAS,GAAG,MAAM,kBAAkB,CAAC,sBAAsB,EAAE;YACjE,kBAAkB,EAAE,OAAO;SAC5B,CAAC,CAAC;QACH,MAAM,QAAQ,GAAG;;;;;;CAMpB,CAAC,IAAI,EAAE,CAAC;QACL,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6DAA6D,EAAE,KAAK,IAAI,EAAE;QAC3E,MAAM,SAAS,GAAG,MAAM,kBAAkB,CAAC,sBAAsB,EAAE;YACjE,QAAQ,EAAE,CAAC;SACZ,CAAC,CAAC;QACH,MAAM,QAAQ,GAAG;;;;;;;CAOpB,CAAC,IAAI,EAAE,CAAC;QACL,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;QAChE,MAAM,SAAS,GAAG,MAAM,kBAAkB,CAAC,uBAAuB,EAAE;YAClE,QAAQ,EAAE,CAAC;SACZ,CAAC,CAAC;QACH,MAAM,eAAe,GAAG;;;;;;;;;CAS3B,CAAC,IAAI,EAAE,CAAC;QACL,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gEAAgE,EAAE,KAAK,IAAI,EAAE;QAC9E,MAAM,SAAS,GAAG,MAAM,kBAAkB,CAAC,sBAAsB,EAAE;YACjE,QAAQ,EAAE,CAAC;SACZ,CAAC,CAAC;QACH,MAAM,mBAAmB,GAAG;;;;;;;CAO/B,CAAC,IAAI,EAAE,CAAC;QACL,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;QACpD,0EAA0E;QAC1E,MAAM,eAAe,GAAG,UAAU,CAAC,OAAO,CAAC;QAC1C,UAAU,CAAC,OAAgB,CAAC,kBAAkB,CAC7C,KAAK,EAAE,CAAwB,EAAE,EAAE;YACjC,IAAI,CAAC,KAAK,cAAc,EAAE,CAAC;gBACzB,MAAM,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC/D,CAAC;YACD,OAAO,eAAe,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CACF,CAAC;QAEF,MAAM,SAAS,GAAG,MAAM,kBAAkB,CAAC,cAAc,CAAC,CAAC;QAC3D,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CACzB,gDAAgD,CACjD,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;QACjE,MAAM,SAAS,GAAG,MAAM,kBAAkB,CAAC,uBAAuB,EAAE;YAClE,QAAQ,EAAE,EAAE;SACb,CAAC,CAAC;QACH,MAAM,QAAQ,GAAG;;;;;;;;CAQpB,CAAC,IAAI,EAAE,CAAC;QACL,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,4DAA4D,EAAE,KAAK,IAAI,EAAE;QAC1E,MAAM,SAAS,GAAG,MAAM,kBAAkB,CAAC,uBAAuB,EAAE;YAClE,QAAQ,EAAE,CAAC;SACZ,CAAC,CAAC;QACH,MAAM,QAAQ,GAAG;;;;;;;CAOpB,CAAC,IAAI,EAAE,CAAC;QACL,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;IAC5C,UAAU,CAAC,GAAG,EAAE;QACd,EAAE,CAAC,aAAa,EAAE,CAAC;QAClB,IAAI,CAAC,OAAgB,CAAC,kBAAkB,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;QAE/D,UAAU,CAAC,OAAgB,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;YAC1D,MAAM,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC1B,IAAI,IAAI,KAAK,eAAe,EAAE,CAAC;gBAC7B,OAAO;oBACL,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC;oBACjC,YAAY,CAAC,cAAc,EAAE,KAAK,CAAC;oBACnC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;oBACnC,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC;iBACxB,CAAC;YACX,CAAC;YACD,IAAI,IAAI,KAAK,4BAA4B,EAAE,CAAC;gBAC1C,OAAO,CAAC,YAAY,CAAC,cAAc,EAAE,KAAK,CAAC,CAAQ,CAAC;YACtD,CAAC;YACD,IAAI,IAAI,KAAK,uBAAuB,EAAE,CAAC;gBACrC,OAAO;oBACL,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;oBACnC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC;iBAC3B,CAAC;YACX,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;QAEF,EAAE,CAAC,YAAqB,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,EAAE;YACjD,MAAM,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC1B,IAAI,IAAI,KAAK,0BAA0B,EAAE,CAAC;gBACxC,OAAO,6DAA6D,CAAC;YACvE,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;QACvE,MAAM,WAAW,GAAG,IAAI,oBAAoB,CAAC,eAAe,CAAC,CAAC;QAC9D,MAAM,SAAS,GAAG,MAAM,kBAAkB,CAAC,eAAe,EAAE;YAC1D,WAAW;SACZ,CAAC,CAAC;QACH,MAAM,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAC/C,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;QAChD,MAAM,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;IAC/C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;QACpE,MAAM,WAAW,GAAG,IAAI,oBAAoB,CAAC,eAAe,CAAC,CAAC;QAC9D,MAAM,SAAS,GAAG,MAAM,kBAAkB,CAAC,eAAe,EAAE;YAC1D,WAAW;YACX,gBAAgB,EAAE,KAAK;SACxB,CAAC,CAAC;QACH,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAC3C,2CAA2C;QAC3C,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}