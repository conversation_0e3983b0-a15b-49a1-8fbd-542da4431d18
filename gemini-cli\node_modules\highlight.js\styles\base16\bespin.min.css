/*!
  Theme: Bespin
  Author: <PERSON>
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#8a8986;background:#28211c}.hljs ::selection,.hljs::selection{background-color:#5e5d5c;color:#8a8986}.hljs-comment{color:#666}.hljs-tag{color:#797977}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#8a8986}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#cf6a4c}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#cf7d34}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#f9ee98}.hljs-strong{font-weight:700;color:#f9ee98}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#54be0d}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#afc4db}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#5ea6ea}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#9b859d}.hljs-emphasis{color:#9b859d;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#937121}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}