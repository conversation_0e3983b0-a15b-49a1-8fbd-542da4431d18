pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Atelier Heath
  Author: <PERSON> (http://atelierbramdehaan.nl)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme atelier-heath
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #1b181b  Default Background
base01  #292329  Lighter Background (Used for status bars, line number and folding marks)
base02  #695d69  Selection Background
base03  #776977  Comments, Invisibles, Line Highlighting
base04  #9e8f9e  Dark Foreground (Used for status bars)
base05  #ab9bab  Default Foreground, Caret, Delimiters, Operators
base06  #d8cad8  Light Foreground (Not often used)
base07  #f7f3f7  Light Background (Not often used)
base08  #ca402b  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #a65926  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #bb8a35  Classes, Markup Bold, Search Text Background
base0B  #918b3b  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #159393  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #516aec  Functions, Methods, Attribute IDs, Headings
base0E  #7b59c0  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #cc33cc  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #ab9bab;
  background: #1b181b
}
.hljs::selection,
.hljs ::selection {
  background-color: #695d69;
  color: #ab9bab
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #776977 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #776977
}
/* base04 - #9e8f9e -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #9e8f9e
}
/* base05 - #ab9bab -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #ab9bab
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #ca402b
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #a65926
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #bb8a35
}
.hljs-strong {
  font-weight: bold;
  color: #bb8a35
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #918b3b
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #159393
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #516aec
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #7b59c0
}
.hljs-emphasis {
  color: #7b59c0;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #cc33cc
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}