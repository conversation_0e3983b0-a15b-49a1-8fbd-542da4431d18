{"version": 3, "file": "telemetry.test.js", "sourceRoot": "", "sources": ["../../../src/telemetry/telemetry.test.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,QAAQ,CAAC;AACzE,OAAO,EACL,mBAAmB,EACnB,iBAAiB,EACjB,yBAAyB,GAC1B,MAAM,UAAU,CAAC;AAClB,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAElD,EAAE,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;AACnC,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAE/B,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;IACzB,IAAI,UAAkB,CAAC;IACvB,IAAI,WAAoB,CAAC;IAEzB,UAAU,CAAC,GAAG,EAAE;QACd,EAAE,CAAC,aAAa,EAAE,CAAC;QAEnB,UAAU,GAAG,IAAI,MAAM,CAAC;YACtB,SAAS,EAAE,iBAAiB;YAC5B,KAAK,EAAE,YAAY;YACnB,SAAS,EAAE,WAAW;YACtB,SAAS,EAAE,KAAK;YAChB,GAAG,EAAE,WAAW;SACjB,CAAC,CAAC;QACH,EAAE,CAAC,KAAK,CAAC,UAAU,EAAE,qBAAqB,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAClE,EAAE,CAAC,KAAK,CAAC,UAAU,EAAE,0BAA0B,CAAC,CAAC,eAAe,CAC9D,uBAAuB,CACxB,CAAC;QACF,EAAE,CAAC,KAAK,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;QACxE,WAAW,GAAG;YACZ,KAAK,EAAE,EAAE,CAAC,EAAE,EAAE;YACd,QAAQ,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;SACzB,CAAC;QACxB,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,sDAAsD;QACtD,IAAI,yBAAyB,EAAE,EAAE,CAAC;YAChC,MAAM,iBAAiB,EAAE,CAAC;QAC5B,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;QACjD,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAChC,MAAM,CAAC,OAAO,CAAC,CAAC,gBAAgB,EAAE,CAAC;QACnC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,CAAC;IAC/C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;QACrD,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAChC,MAAM,iBAAiB,EAAE,CAAC;QAE1B,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,gBAAgB,EAAE,CAAC;IAClD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}