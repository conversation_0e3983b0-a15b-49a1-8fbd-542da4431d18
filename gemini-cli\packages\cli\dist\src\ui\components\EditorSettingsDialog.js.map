{"version": 3, "file": "EditorSettingsDialog.js", "sourceRoot": "", "sources": ["../../../../src/ui/components/EditorSettingsDialog.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAc,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AACxC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAC;AAC1C,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EACL,oBAAoB,EACpB,qBAAqB,GAEtB,MAAM,qCAAqC,CAAC;AAC7C,OAAO,EAAE,iBAAiB,EAAE,MAAM,+BAA+B,CAAC;AAClE,OAAO,EAAkB,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxE,OAAO,EAAc,iBAAiB,EAAE,MAAM,oBAAoB,CAAC;AAQnE,MAAM,UAAU,oBAAoB,CAAC,EACnC,QAAQ,EACR,QAAQ,EACR,MAAM,GACY;IAClB,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,QAAQ,CAChD,YAAY,CAAC,IAAI,CAClB,CAAC;IACF,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,QAAQ,CAClD,QAAQ,CACT,CAAC;IACF,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE;QAClB,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC;YACZ,iBAAiB,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;QACxE,CAAC;QACD,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,MAAM,EAAE,CAAC;QACX,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,WAAW,GACf,qBAAqB,CAAC,0BAA0B,EAAE,CAAC;IAErD,MAAM,iBAAiB,GACrB,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC;IAC5D,IAAI,WAAW,GAAG,iBAAiB;QACjC,CAAC,CAAC,WAAW,CAAC,SAAS,CACnB,CAAC,IAAmB,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,iBAAiB,CACzD;QACH,CAAC,CAAC,CAAC,CAAC;IACN,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE,CAAC;QACvB,OAAO,CAAC,KAAK,CAAC,4BAA4B,iBAAiB,EAAE,CAAC,CAAC;QAC/D,WAAW,GAAG,CAAC,CAAC;IAClB,CAAC;IAED,MAAM,UAAU,GAAG;QACjB,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,YAAY,CAAC,IAAI,EAAE;QACpD,EAAE,KAAK,EAAE,oBAAoB,EAAE,KAAK,EAAE,YAAY,CAAC,SAAS,EAAE;KAC/D,CAAC;IAEF,MAAM,kBAAkB,GAAG,CAAC,UAAkC,EAAE,EAAE;QAChE,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;YAC7B,QAAQ,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;YACnC,OAAO;QACT,CAAC;QACD,QAAQ,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;IACtC,CAAC,CAAC;IAEF,MAAM,iBAAiB,GAAG,CAAC,KAAmB,EAAE,EAAE;QAChD,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACxB,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC,CAAC;IAEF,IAAI,yBAAyB,GAAG,EAAE,CAAC;IACnC,MAAM,UAAU,GACd,aAAa,KAAK,YAAY,CAAC,IAAI;QACjC,CAAC,CAAC,YAAY,CAAC,SAAS;QACxB,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC;IACxB,IAAI,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;QACzE,yBAAyB;YACvB,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,eAAe,KAAK,SAAS;gBACrE,CAAC,CAAC,qBAAqB,UAAU,GAAG;gBACpC,CAAC,CAAC,gBAAgB,UAAU,GAAG,CAAC;IACtC,CAAC;IAED,IAAI,gBAAgB,GAAG,MAAM,CAAC;IAC9B,IACE,QAAQ,CAAC,MAAM,CAAC,eAAe;QAC/B,iBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,EAClD,CAAC;QACD,gBAAgB;YACd,oBAAoB,CAAC,QAAQ,CAAC,MAAM,CAAC,eAA6B,CAAC,CAAC;IACxE,CAAC;IAED,OAAO,CACL,MAAC,GAAG,IACF,WAAW,EAAC,OAAO,EACnB,WAAW,EAAE,MAAM,CAAC,IAAI,EACxB,aAAa,EAAC,KAAK,EACnB,OAAO,EAAE,CAAC,EACV,KAAK,EAAC,MAAM,aAEZ,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,KAAK,EAAC,KAAK,EAAC,YAAY,EAAE,CAAC,aACrD,MAAC,IAAI,IAAC,IAAI,EAAE,cAAc,KAAK,QAAQ,aACpC,cAAc,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,mBAAe,GAAG,EAC5D,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,YAAG,yBAAyB,GAAQ,IACvD,EACP,KAAC,iBAAiB,IAChB,KAAK,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;4BAChC,KAAK,EAAE,IAAI,CAAC,IAAI;4BAChB,KAAK,EAAE,IAAI,CAAC,IAAI;4BAChB,QAAQ,EAAE,IAAI,CAAC,QAAQ;yBACxB,CAAC,CAAC,EACH,YAAY,EAAE,WAAW,EACzB,QAAQ,EAAE,kBAAkB,EAC5B,SAAS,EAAE,cAAc,KAAK,QAAQ,IACjC,aAAa,CAClB,EAEF,MAAC,GAAG,IAAC,SAAS,EAAE,CAAC,EAAE,aAAa,EAAC,QAAQ,aACvC,MAAC,IAAI,IAAC,IAAI,EAAE,cAAc,KAAK,OAAO,aACnC,cAAc,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,gBACpC,EACP,KAAC,iBAAiB,IAChB,KAAK,EAAE,UAAU,EACjB,YAAY,EAAE,CAAC,EACf,QAAQ,EAAE,iBAAiB,EAC3B,SAAS,EAAE,cAAc,KAAK,OAAO,GACrC,IACE,EAEN,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,2DAEjB,GACH,IACF,EAEN,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,KAAK,EAAC,KAAK,EAAC,WAAW,EAAE,CAAC,aACpD,KAAC,IAAI,IAAC,IAAI,wCAAyB,EACnC,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,aAC9C,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,qHAGjB,EACP,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,0CACI,GAAG,EAC7B,KAAC,IAAI,IACH,KAAK,EACH,gBAAgB,KAAK,MAAM;4CACzB,CAAC,CAAC,MAAM,CAAC,SAAS;4CAClB,CAAC,CAAC,MAAM,CAAC,UAAU,EAEvB,IAAI,kBAEH,gBAAgB,GACZ,SAEF,IACH,IACF,IACF,CACP,CAAC;AACJ,CAAC"}