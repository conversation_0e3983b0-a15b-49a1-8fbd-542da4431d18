{"version": 3, "file": "ToolConfirmationMessage.js", "sourceRoot": "", "sources": ["../../../../../src/ui/components/messages/ToolConfirmationMessage.tsx"], "names": [], "mappings": ";AAOA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAC;AAC1C,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AACzC,OAAO,EAEL,uBAAuB,GAIxB,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EACL,iBAAiB,GAElB,MAAM,gCAAgC,CAAC;AACxC,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AAUvD,MAAM,CAAC,MAAM,uBAAuB,GAEhC,CAAC,EACH,mBAAmB,EACnB,SAAS,GAAG,IAAI,EAChB,uBAAuB,EACvB,aAAa,GACd,EAAE,EAAE;IACH,MAAM,EAAE,SAAS,EAAE,GAAG,mBAAmB,CAAC;IAC1C,MAAM,UAAU,GAAG,aAAa,GAAG,CAAC,CAAC,CAAC,gBAAgB;IAEtD,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE;QAClB,IAAI,CAAC,SAAS;YAAE,OAAO;QACvB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,SAAS,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,YAAY,GAAG,CAAC,IAA6B,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAExE,IAAI,WAAW,GAA2B,IAAI,CAAC,CAAC,8BAA8B;IAC9E,IAAI,QAAgB,CAAC;IAErB,MAAM,OAAO,GAAoD,IAAI,KAAK,EAEvE,CAAC;IAEJ,+DAA+D;IAC/D,wEAAwE;IAExE,SAAS,0BAA0B;QACjC,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,wFAAwF;YACxF,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,uBAAuB,KAAK,SAAS,EAAE,CAAC;YAC1C,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,kEAAkE;QAClE,qCAAqC;QACrC,MAAM,eAAe,GAAG,CAAC,CAAC,CAAC,mDAAmD;QAC9E,MAAM,kBAAkB,GAAG,CAAC,CAAC,CAAC,gCAAgC;QAC9D,MAAM,eAAe,GAAG,CAAC,CAAC,CAAC,iCAAiC;QAC5D,MAAM,sBAAsB,GAAG,CAAC,CAAC,CAAC,oCAAoC;QACtE,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,kDAAkD;QAEzF,MAAM,yBAAyB,GAC7B,eAAe;YACf,kBAAkB;YAClB,eAAe;YACf,sBAAsB;YACtB,cAAc,CAAC;QACjB,OAAO,IAAI,CAAC,GAAG,CAAC,uBAAuB,GAAG,yBAAyB,EAAE,CAAC,CAAC,CAAC;IAC1E,CAAC;IACD,IAAI,mBAAmB,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;QACxC,IAAI,mBAAmB,CAAC,WAAW,EAAE,CAAC;YACpC,OAAO,CACL,MAAC,GAAG,IACF,QAAQ,EAAC,KAAK,EACd,WAAW,EAAC,OAAO,EACnB,WAAW,EAAE,MAAM,CAAC,IAAI,EACxB,cAAc,EAAC,cAAc,EAC7B,OAAO,EAAE,CAAC,EACV,QAAQ,EAAC,QAAQ,aAEjB,KAAC,IAAI,uCAA4B,EACjC,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,WAAW,2DAExB,IACH,CACP,CAAC;QACJ,CAAC;QAED,QAAQ,GAAG,oBAAoB,CAAC;QAChC,OAAO,CAAC,IAAI,CACV;YACE,KAAK,EAAE,iBAAiB;YACxB,KAAK,EAAE,uBAAuB,CAAC,WAAW;SAC3C,EACD;YACE,KAAK,EAAE,mBAAmB;YAC1B,KAAK,EAAE,uBAAuB,CAAC,aAAa;SAC7C,EACD;YACE,KAAK,EAAE,6BAA6B;YACpC,KAAK,EAAE,uBAAuB,CAAC,gBAAgB;SAChD,EACD,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,uBAAuB,CAAC,MAAM,EAAE,CAC7D,CAAC;QACF,WAAW,GAAG,CACZ,KAAC,YAAY,IACX,WAAW,EAAE,mBAAmB,CAAC,QAAQ,EACzC,QAAQ,EAAE,mBAAmB,CAAC,QAAQ,EACtC,uBAAuB,EAAE,0BAA0B,EAAE,EACrD,aAAa,EAAE,UAAU,GACzB,CACH,CAAC;IACJ,CAAC;SAAM,IAAI,mBAAmB,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;QAC/C,MAAM,cAAc,GAClB,mBAAqD,CAAC;QAExD,QAAQ,GAAG,kBAAkB,CAAC;QAC9B,OAAO,CAAC,IAAI,CACV;YACE,KAAK,EAAE,iBAAiB;YACxB,KAAK,EAAE,uBAAuB,CAAC,WAAW;SAC3C,EACD;YACE,KAAK,EAAE,sBAAsB,cAAc,CAAC,WAAW,OAAO;YAC9D,KAAK,EAAE,uBAAuB,CAAC,aAAa;SAC7C,EACD,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,uBAAuB,CAAC,MAAM,EAAE,CAC7D,CAAC;QAEF,IAAI,iBAAiB,GAAG,0BAA0B,EAAE,CAAC;QACrD,IAAI,iBAAiB,KAAK,SAAS,EAAE,CAAC;YACpC,iBAAiB,IAAI,CAAC,CAAC,CAAC,uBAAuB;QACjD,CAAC;QACD,WAAW,GAAG,CACZ,KAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,YACzB,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,YAC7B,KAAC,WAAW,IACV,SAAS,EAAE,iBAAiB,EAC5B,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC,CAAC,YAErC,KAAC,GAAG,cACF,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,YAAG,cAAc,CAAC,OAAO,GAAQ,GAC3D,GACM,GACV,GACF,CACP,CAAC;IACJ,CAAC;SAAM,IAAI,mBAAmB,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;QAC/C,MAAM,SAAS,GAAG,mBAAmB,CAAC;QACtC,MAAM,WAAW,GACf,SAAS,CAAC,IAAI;YACd,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,MAAM,CAAC,CAAC;QAE3E,QAAQ,GAAG,yBAAyB,CAAC;QACrC,OAAO,CAAC,IAAI,CACV;YACE,KAAK,EAAE,iBAAiB;YACxB,KAAK,EAAE,uBAAuB,CAAC,WAAW;SAC3C,EACD;YACE,KAAK,EAAE,mBAAmB;YAC1B,KAAK,EAAE,uBAAuB,CAAC,aAAa;SAC7C,EACD,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,uBAAuB,CAAC,MAAM,EAAE,CAC7D,CAAC;QAEF,WAAW,GAAG,CACZ,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,aACpD,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,YAAG,SAAS,CAAC,MAAM,GAAQ,EACxD,WAAW,IAAI,SAAS,CAAC,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAC7D,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,SAAS,EAAE,CAAC,aACtC,KAAC,IAAI,iCAAsB,EAC1B,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAC3B,MAAC,IAAI,sBAAe,GAAG,KAAZ,GAAG,CAAiB,CAChC,CAAC,IACE,CACP,IACG,CACP,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,wBAAwB;QACxB,MAAM,QAAQ,GAAG,mBAAiD,CAAC;QAEnE,WAAW,GAAG,CACZ,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,aACpD,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,6BAAe,QAAQ,CAAC,UAAU,IAAQ,EACxE,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,uBAAS,QAAQ,CAAC,QAAQ,IAAQ,IAC5D,CACP,CAAC;QAEF,QAAQ,GAAG,gCAAgC,QAAQ,CAAC,QAAQ,kBAAkB,QAAQ,CAAC,UAAU,IAAI,CAAC;QACtG,OAAO,CAAC,IAAI,CACV;YACE,KAAK,EAAE,iBAAiB;YACxB,KAAK,EAAE,uBAAuB,CAAC,WAAW;SAC3C,EACD;YACE,KAAK,EAAE,2BAA2B,QAAQ,CAAC,QAAQ,kBAAkB,QAAQ,CAAC,UAAU,GAAG;YAC3F,KAAK,EAAE,uBAAuB,CAAC,iBAAiB,EAAE,+BAA+B;SAClF,EACD;YACE,KAAK,EAAE,4CAA4C,QAAQ,CAAC,UAAU,GAAG;YACzE,KAAK,EAAE,uBAAuB,CAAC,mBAAmB;SACnD,EACD,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,uBAAuB,CAAC,MAAM,EAAE,CAC7D,CAAC;IACJ,CAAC;IAED,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,UAAU,aAGvD,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,QAAQ,EAAC,QAAQ,EAAC,YAAY,EAAE,CAAC,YAC/D,WAAW,GACR,EAGN,KAAC,GAAG,IAAC,YAAY,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,YACjC,KAAC,IAAI,IAAC,IAAI,EAAC,UAAU,YAAE,QAAQ,GAAQ,GACnC,EAGN,KAAC,GAAG,IAAC,UAAU,EAAE,CAAC,YAChB,KAAC,iBAAiB,IAChB,KAAK,EAAE,OAAO,EACd,QAAQ,EAAE,YAAY,EACtB,SAAS,EAAE,SAAS,GACpB,GACE,IACF,CACP,CAAC;AACJ,CAAC,CAAC"}