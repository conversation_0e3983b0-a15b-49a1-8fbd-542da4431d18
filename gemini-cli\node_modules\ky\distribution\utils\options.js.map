{"version": 3, "file": "options.js", "sourceRoot": "", "sources": ["../../source/utils/options.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,YAAY,EAAE,sBAAsB,EAAC,MAAM,sBAAsB,CAAC;AAE1E,MAAM,CAAC,MAAM,kBAAkB,GAAG,CACjC,OAAgB,EAChB,OAAgC,EACN,EAAE;IAC5B,MAAM,cAAc,GAA4B,EAAE,CAAC;IAEnD,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;QAC3B,IAAI,CAAC,CAAC,GAAG,IAAI,sBAAsB,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,OAAO,CAAC,EAAE,CAAC;YACrF,cAAc,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;QACpC,CAAC;IACF,CAAC;IAED,OAAO,cAAc,CAAC;AACvB,CAAC,CAAC", "sourcesContent": ["import {kyOptionKeys, requestOptionsRegistry} from '../core/constants.js';\n\nexport const findUnknownOptions = (\n\trequest: Request,\n\toptions: Record<string, unknown>,\n): Record<string, unknown> => {\n\tconst unknownOptions: Record<string, unknown> = {};\n\n\tfor (const key in options) {\n\t\tif (!(key in requestOptionsRegistry) && !(key in kyOptionKeys) && !(key in request)) {\n\t\t\tunknownOptions[key] = options[key];\n\t\t}\n\t}\n\n\treturn unknownOptions;\n};\n"]}