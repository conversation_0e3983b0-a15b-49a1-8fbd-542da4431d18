{"version": 3, "file": "useTimer.js", "sourceRoot": "", "sources": ["../../../../src/ui/hooks/useTimer.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC;AAEpD;;;;;GAKG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,QAAiB,EAAE,QAAiB,EAAE,EAAE;IAC/D,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAClD,MAAM,QAAQ,GAAG,MAAM,CAAwB,IAAI,CAAC,CAAC;IACrD,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;IACzC,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;IAEzC,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,eAAe,GAAG,KAAK,CAAC;QAE5B,IAAI,eAAe,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;YACzC,eAAe,GAAG,IAAI,CAAC;YACvB,eAAe,CAAC,OAAO,GAAG,QAAQ,CAAC;QACrC,CAAC;QAED,IAAI,eAAe,CAAC,OAAO,KAAK,KAAK,IAAI,QAAQ,EAAE,CAAC;YAClD,uCAAuC;YACvC,eAAe,GAAG,IAAI,CAAC;QACzB,CAAC;QAED,IAAI,eAAe,EAAE,CAAC;YACpB,cAAc,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC;QACD,eAAe,CAAC,OAAO,GAAG,QAAQ,CAAC;QAEnC,kBAAkB;QAClB,IAAI,QAAQ,EAAE,CAAC;YACb,oEAAoE;YACpE,+EAA+E;YAC/E,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAClC,CAAC;YACD,QAAQ,CAAC,OAAO,GAAG,WAAW,CAAC,GAAG,EAAE;gBAClC,cAAc,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YACrC,CAAC,EAAE,IAAI,CAAC,CAAC;QACX,CAAC;aAAM,CAAC;YACN,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBAChC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,OAAO,GAAG,EAAE;YACV,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBAChC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;IAEzB,OAAO,WAAW,CAAC;AACrB,CAAC,CAAC"}