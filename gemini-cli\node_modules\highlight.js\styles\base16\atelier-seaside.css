pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Atelier Seaside
  Author: <PERSON> (http://atelierbramdehaan.nl)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme atelier-seaside
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #131513  Default Background
base01  #242924  Lighter Background (Used for status bars, line number and folding marks)
base02  #5e6e5e  Selection Background
base03  #687d68  Comments, Invisibles, Line Highlighting
base04  #809980  Dark Foreground (Used for status bars)
base05  #8ca68c  Default Foreground, Caret, Delimiters, Operators
base06  #cfe8cf  Light Foreground (Not often used)
base07  #f4fbf4  Light Background (Not often used)
base08  #e6193c  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #87711d  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #98981b  Classes, Markup Bold, Search Text Background
base0B  #29a329  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #1999b3  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #3d62f5  Functions, Methods, Attribute IDs, Headings
base0E  #ad2bee  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #e619c3  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #8ca68c;
  background: #131513
}
.hljs::selection,
.hljs ::selection {
  background-color: #5e6e5e;
  color: #8ca68c
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #687d68 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #687d68
}
/* base04 - #809980 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #809980
}
/* base05 - #8ca68c -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #8ca68c
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #e6193c
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #87711d
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #98981b
}
.hljs-strong {
  font-weight: bold;
  color: #98981b
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #29a329
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #1999b3
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #3d62f5
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #ad2bee
}
.hljs-emphasis {
  color: #ad2bee;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #e619c3
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}