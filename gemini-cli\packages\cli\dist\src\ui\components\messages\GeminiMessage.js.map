{"version": 3, "file": "GeminiMessage.js", "sourceRoot": "", "sources": ["../../../../../src/ui/components/messages/GeminiMessage.tsx"], "names": [], "mappings": ";AAOA,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAChC,OAAO,EAAE,eAAe,EAAE,MAAM,gCAAgC,CAAC;AACjE,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AASzC,MAAM,CAAC,MAAM,aAAa,GAAiC,CAAC,EAC1D,IAAI,EACJ,SAAS,EACT,uBAAuB,EACvB,aAAa,GACd,EAAE,EAAE;IACH,MAAM,MAAM,GAAG,IAAI,CAAC;IACpB,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;IAElC,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,KAAK,aACtB,KAAC,GAAG,IAAC,KAAK,EAAE,WAAW,YACrB,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,YAAY,YAAG,MAAM,GAAQ,GAC7C,EACN,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,aAAa,EAAC,QAAQ,YACtC,KAAC,eAAe,IACd,IAAI,EAAE,IAAI,EACV,SAAS,EAAE,SAAS,EACpB,uBAAuB,EAAE,uBAAuB,EAChD,aAAa,EAAE,aAAa,GAC5B,GACE,IACF,CACP,CAAC;AACJ,CAAC,CAAC"}