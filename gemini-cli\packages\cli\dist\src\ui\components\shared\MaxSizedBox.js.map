{"version": 3, "file": "MaxSizedBox.js", "sourceRoot": "", "sources": ["../../../../../src/ui/components/shared/MaxSizedBox.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,OAAO,CAAC;AAC1D,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AAChC,OAAO,WAAW,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AACzC,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxD,OAAO,EAAE,kBAAkB,EAAE,MAAM,mCAAmC,CAAC;AAEvE,IAAI,cAAc,GAAG,KAAK,CAAC;AAE3B;;;;GAIG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,CAAC,CAAC;AAEpC,MAAM,UAAU,uBAAuB,CAAC,KAAc;IACpD,cAAc,GAAG,KAAK,CAAC;AACzB,CAAC;AAED,SAAS,gBAAgB,CAAC,OAAe,EAAE,OAAwB;IACjE,IAAI,CAAC,cAAc;QAAE,OAAO;IAE5B,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;QACnC,OAAO,CAAC,KAAK,CACX,OAAO,EACP,qBAAqB,MAAM,CAAC,OAAO,CAAC,YAAY,OAAO,OAAO,EAAE,CACjE,CAAC;QACF,OAAO;IACT,CAAC;IAED,IAAI,aAAa,GAAG,gBAAgB,CAAC;IACrC,IAAI,CAAC;QACH,MAAM,iBAAiB,GAAG,OAEzB,CAAC;QACF,MAAM,QAAQ,GAAG,iBAAiB,CAAC,OAAO,EAAE,QAAQ,CAAC;QACrD,MAAM,UAAU,GAAG,iBAAiB,CAAC,OAAO,EAAE,UAAU,CAAC;QACzD,aAAa,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,IAAI,UAAU,EAAE,CAAC,CAAC,CAAC,gBAAgB,CAAC;IAC5E,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;IAC/D,CAAC;IAED,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,aAAa,EAAE,CAAC,CAAC;AAC9E,CAAC;AASD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAsCG;AACH,MAAM,CAAC,MAAM,WAAW,GAA+B,CAAC,EACtD,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,iBAAiB,GAAG,KAAK,EACzB,0BAA0B,GAAG,CAAC,GAC/B,EAAE,EAAE;IACH,MAAM,EAAE,GAAG,KAAK,EAAE,CAAC;IACnB,MAAM,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,GAAG,kBAAkB,EAAE,IAAI,EAAE,CAAC;IAE7E,MAAM,iBAAiB,GAAmB,EAAE,CAAC;IAC7C,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAC9B,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,MAAM,CAAC,gBAAgB,CAAC,EAChD,kBAAkB,CACnB,CAAC;IAEF,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;QAC3B,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;IACrE,CAAC;IACD,SAAS,SAAS,CAAC,OAAwB;QACzC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;YACnC,OAAO;QACT,CAAC;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC9B,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YAC1D,OAAO;QACT,CAAC;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;YACzB,4BAA4B,CAAC,OAAO,EAAE,QAAS,EAAE,iBAAiB,CAAC,CAAC;YACpE,OAAO;QACT,CAAC;QAED,gBAAgB,CAAC,6CAA6C,EAAE,OAAO,CAAC,CAAC;IAC3E,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;IAE5C,MAAM,mBAAmB,GACvB,CAAC,eAAe,KAAK,SAAS;QAC5B,iBAAiB,CAAC,MAAM,GAAG,eAAe,CAAC;QAC7C,0BAA0B,GAAG,CAAC,CAAC;IACjC,MAAM,oBAAoB,GACxB,mBAAmB,IAAI,eAAe,KAAK,SAAS;QAClD,CAAC,CAAC,eAAe,GAAG,CAAC;QACrB,CAAC,CAAC,eAAe,CAAC;IAEtB,MAAM,gBAAgB,GACpB,oBAAoB,KAAK,SAAS;QAChC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,iBAAiB,CAAC,MAAM,GAAG,oBAAoB,CAAC;QAC9D,CAAC,CAAC,CAAC,CAAC;IACR,MAAM,gBAAgB,GAAG,gBAAgB,GAAG,0BAA0B,CAAC;IAEvE,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,gBAAgB,GAAG,CAAC,EAAE,CAAC;YACzB,gBAAgB,EAAE,CAAC,EAAE,CAAC,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,mBAAmB,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5B,CAAC;QAED,OAAO,GAAG,EAAE;YACV,mBAAmB,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5B,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,mBAAmB,CAAC,CAAC,CAAC;IAElE,MAAM,iBAAiB,GACrB,gBAAgB,GAAG,CAAC;QAClB,CAAC,CAAC,iBAAiB,KAAK,KAAK;YAC3B,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,MAAM,CAAC;YACrE,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,oBAAoB,CAAC;QACpD,CAAC,CAAC,iBAAiB,CAAC;IAExB,MAAM,YAAY,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAC1D,KAAC,GAAG,cACD,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CACjB,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,CAAC,CAC9B,KAAC,IAAI,OAAoB,OAAO,CAAC,KAAK,YACnC,OAAO,CAAC,IAAI,IADJ,QAAQ,CAEZ,CACR,CAAC,CACH,CAAC,CAAC,CAAC,CACF,KAAC,IAAI,oBAAS,CACf,IATO,KAAK,CAUT,CACP,CAAC,CAAC;IAEH,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,aACvD,gBAAgB,GAAG,CAAC,IAAI,iBAAiB,KAAK,KAAK,IAAI,CACtD,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,EAAC,UAAU,2BAC5B,gBAAgB,WAAO,gBAAgB,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,kBAEnE,CACR,EACA,YAAY,EACZ,gBAAgB,GAAG,CAAC,IAAI,iBAAiB,KAAK,QAAQ,IAAI,CACzD,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,EAAC,UAAU,0BAC7B,gBAAgB,WAAO,gBAAgB,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,kBAElE,CACR,IACG,CACP,CAAC;AACJ,CAAC,CAAC;AAoBF;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,SAAS,WAAW,CAAC,OAAwB;IAC3C,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;QAC3D,gBAAgB,CACd,oDAAoD,EACpD,OAAO,CACR,CAAC;QACF,OAAO;YACL,cAAc,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;YAChD,QAAQ,EAAE,EAAE;SACb,CAAC;IACJ,CAAC;IAED,IAAI,cAAc,EAAE,CAAC;QACnB,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC;QAC/B,mEAAmE;QACnE,IAAI,gBAAgB,GAAG,CAAC,CAAC;QACzB,IAAI,QAAQ,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACpC,6DAA6D;YAC7D,gBAAgB,IAAI,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,QAAQ,CAAC,aAAa,KAAK,KAAK,EAAE,CAAC;YACrC,gBAAgB,CACd,qDAAqD,EACrD,OAAO,CACR,CAAC;QACJ,CAAC;QACD,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,gBAAgB,EAAE,CAAC;YACpD,gBAAgB,CACd,4DAA4D,MAAM,CAAC,IAAI,CACrE,QAAQ,CACT,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EACd,OAAO,CACR,CAAC;QACJ,CAAC;IACH,CAAC;IAED,MAAM,GAAG,GAAQ;QACf,cAAc,EAAE,EAAE;QAClB,QAAQ,EAAE,EAAE;KACb,CAAC;IAEF,IAAI,cAAc,GAAG,KAAK,CAAC;IAE3B,SAAS,aAAa,CACpB,OAAwB,EACxB,WAAgD;QAEhD,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;YACrB,OAAO;QACT,CAAC;QACD,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAC/D,MAAM,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;YAC7B,0DAA0D;YAC1D,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,OAAO;YACT,CAAC;YAED,MAAM,OAAO,GAAe,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,IAAI,EAAE,EAAE,CAAC;YAE/D,8EAA8E;YAC9E,IAAI,WAAW,KAAK,SAAS,IAAI,WAAW,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBAC7D,cAAc,GAAG,IAAI,CAAC;gBACtB,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC7B,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,cAAc,EAAE,CAAC;oBACpB,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACnC,CAAC;qBAAM,CAAC;oBACN,6EAA6E;oBAC7E,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC3B,gBAAgB,CACd,4FAA4F,EAC5F,OAAO,CACR,CAAC;gBACJ,CAAC;YACH,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;YACnC,gBAAgB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;YAC9C,OAAO;QACT,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC9B,MAAM,gBAAgB,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC;YAChD,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,KAAK,EAAE,EAAE,CACjD,aAAa,CAAC,KAAK,EAAE,WAAW,CAAC,CAClC,CAAC;YACF,OAAO;QACT,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;YAC1B,gBAAgB,CACd,gDAAgD,EAChD,OAAO,CACR,CAAC;YACF,OAAO;QACT,CAAC;QAED,wEAAwE;QACxE,MAAM,EAAE,QAAQ,EAAE,GAAG,YAAY,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC;QACpD,MAAM,WAAW,GACf,WAAW,KAAK,SAAS;YACvB,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,EAAE,GAAG,WAAW,EAAE,GAAG,YAAY,EAAE,CAAC;QAC1C,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CACzC,aAAa,CAAC,KAAK,EAAE,WAAW,CAAC,CAClC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CACvD,aAAa,CAAC,KAAK,EAAE,SAAS,CAAC,CAChC,CAAC;IAEF,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,4BAA4B,CACnC,OAA2B,EAC3B,QAAgB,EAChB,MAAsB;IAEtB,MAAM,GAAG,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;IACjC,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,GAAG,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACjE,iEAAiE;QACjE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAChB,OAAO;IACT,CAAC;IAED,MAAM,KAAK,GAAmB,EAAE,CAAC;IACjC,MAAM,kBAAkB,GAAiB,EAAE,CAAC;IAC5C,IAAI,eAAe,GAAG,CAAC,CAAC;IAExB,2CAA2C;IAC3C,GAAG,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QACrC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACjC,eAAe,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/C,CAAC,CAAC,CAAC;IAEH,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC9B,wEAAwE;QACxE,wCAAwC;QACxC,MAAM,KAAK,GAAmB,EAAE,CAAC;QACjC,IAAI,WAAW,GAAiB,EAAE,CAAC;QACnC,kBAAkB,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACrC,MAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC3C,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;gBAChC,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;oBACd,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBACxB,WAAW,GAAG,EAAE,CAAC;gBACnB,CAAC;gBACD,IAAI,IAAI,EAAE,CAAC;oBACT,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;gBACnD,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,IACE,WAAW,CAAC,MAAM,GAAG,CAAC;YACtB,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC;gBAC5B,kBAAkB,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EACxE,CAAC;YACD,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC1B,CAAC;QACD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC;QACD,OAAO;IACT,CAAC;IAED,MAAM,cAAc,GAAG,QAAQ,GAAG,eAAe,CAAC;IAElD,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC;QACvB,sGAAsG;QACtG,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAChC,OAAO;IACT,CAAC;IAED,qCAAqC;IACrC,IAAI,YAAY,GAAiB,EAAE,CAAC;IACpC,IAAI,iBAAiB,GAAG,CAAC,CAAC;IAE1B,SAAS,sBAAsB;QAC7B,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,kBAAkB,EAAE,GAAG,YAAY,CAAC,CAAC,CAAC;QACvD,CAAC;aAAM,CAAC;YACN,IAAI,eAAe,GAAG,CAAC,EAAE,CAAC;gBACxB,KAAK,CAAC,IAAI,CAAC;oBACT,GAAG,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;oBACrD,GAAG,YAAY;iBAChB,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC;QACD,YAAY,GAAG,EAAE,CAAC;QAClB,iBAAiB,GAAG,CAAC,CAAC;IACxB,CAAC;IAED,SAAS,iBAAiB,CAAC,IAAY,EAAE,KAA8B;QACrE,IACE,YAAY,CAAC,MAAM,GAAG,CAAC;YACvB,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,KAAK,EACrD,CAAC;YACD,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC;QACrD,CAAC;aAAM,CAAC;YACN,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IAED,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC/B,MAAM,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAElD,gBAAgB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE,EAAE;YAC/C,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;gBAClB,sBAAsB,EAAE,CAAC;YAC3B,CAAC;YAED,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,sBAAsB;YAE7D,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBACrB,IAAI,CAAC,IAAI;oBAAE,OAAO;gBAClB,MAAM,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;gBAEpC,IACE,iBAAiB,GAAG,SAAS,GAAG,cAAc;oBAC9C,iBAAiB,GAAG,CAAC,EACrB,CAAC;oBACD,sBAAsB,EAAE,CAAC;oBACzB,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;wBACvB,OAAO;oBACT,CAAC;gBACH,CAAC;gBAED,IAAI,SAAS,GAAG,cAAc,EAAE,CAAC;oBAC/B,mDAAmD;oBACnD,MAAM,gBAAgB,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;oBAC5C,IAAI,yBAAyB,GAAG,gBAAgB,CAAC;oBACjD,OAAO,yBAAyB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC5C,IAAI,UAAU,GAAG,CAAC,CAAC;wBACnB,IAAI,iBAAiB,GAAG,CAAC,CAAC;wBAC1B,KAAK,MAAM,IAAI,IAAI,yBAAyB,EAAE,CAAC;4BAC7C,MAAM,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;4BACpC,IACE,iBAAiB,GAAG,iBAAiB,GAAG,SAAS;gCACjD,cAAc,EACd,CAAC;gCACD,MAAM;4BACR,CAAC;4BACD,iBAAiB,IAAI,SAAS,CAAC;4BAC/B,UAAU,EAAE,CAAC;wBACf,CAAC;wBAED,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;4BACnB,MAAM,IAAI,GAAG,yBAAyB;iCACnC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC;iCACpB,IAAI,CAAC,EAAE,CAAC,CAAC;4BACZ,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;4BACvC,iBAAiB,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;4BACvC,yBAAyB;gCACvB,yBAAyB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;wBAChD,CAAC;wBAED,IAAI,yBAAyB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BACzC,sBAAsB,EAAE,CAAC;wBAC3B,CAAC;oBACH,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;oBACvC,iBAAiB,IAAI,SAAS,CAAC;gBACjC,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,+DAA+D;QAC/D,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,sBAAsB,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC5B,sBAAsB,EAAE,CAAC;IAC3B,CAAC;IACD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;AACH,CAAC"}