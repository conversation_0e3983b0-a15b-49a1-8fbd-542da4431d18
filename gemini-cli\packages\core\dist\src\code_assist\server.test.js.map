{"version": 3, "file": "server.test.js", "sourceRoot": "", "sources": ["../../../src/code_assist/server.test.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,QAAQ,CAAC;AAClD,OAAO,EAAE,gBAAgB,EAAE,MAAM,aAAa,CAAC;AAC/C,OAAO,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;AAEnD,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAE/B,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAChC,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAC1C,MAAM,IAAI,GAAG,IAAI,YAAY,EAAE,CAAC;QAChC,MAAM,MAAM,GAAG,IAAI,gBAAgB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;QAC1D,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;QACxD,MAAM,IAAI,GAAG,IAAI,YAAY,EAAE,CAAC;QAChC,MAAM,MAAM,GAAG,IAAI,gBAAgB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;QAC1D,MAAM,YAAY,GAAG;YACnB,QAAQ,EAAE;gBACR,UAAU,EAAE;oBACV;wBACE,KAAK,EAAE,CAAC;wBACR,OAAO,EAAE;4BACP,IAAI,EAAE,OAAO;4BACb,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;yBAC9B;wBACD,YAAY,EAAE,MAAM;wBACpB,aAAa,EAAE,EAAE;qBAClB;iBACF;aACF;SACF,CAAC;QACF,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAEjE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC;YAC5C,KAAK,EAAE,YAAY;YACnB,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;SAC3D,CAAC,CAAC;QAEH,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAC9C,iBAAiB,EACjB,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAClB,SAAS,CACV,CAAC;QACF,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,IAAI,CAC9D,UAAU,CACX,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;QAC9D,MAAM,IAAI,GAAG,IAAI,YAAY,EAAE,CAAC;QAChC,MAAM,MAAM,GAAG,IAAI,gBAAgB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;QAC1D,MAAM,YAAY,GAAG,CAAC,KAAK,SAAS,CAAC;YACnC,MAAM;gBACJ,QAAQ,EAAE;oBACR,UAAU,EAAE;wBACV;4BACE,KAAK,EAAE,CAAC;4BACR,OAAO,EAAE;gCACP,IAAI,EAAE,OAAO;gCACb,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;6BAC9B;4BACD,YAAY,EAAE,MAAM;4BACpB,aAAa,EAAE,EAAE;yBAClB;qBACF;iBACF;aACF,CAAC;QACJ,CAAC,CAAC,EAAE,CAAC;QACL,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAEnE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,qBAAqB,CAAC;YAChD,KAAK,EAAE,YAAY;YACnB,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;SAC3D,CAAC,CAAC;QAEH,IAAI,KAAK,EAAE,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,oBAAoB,CAChD,uBAAuB,EACvB,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAClB,SAAS,CACV,CAAC;YACF,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;QACpD,MAAM,IAAI,GAAG,IAAI,YAAY,EAAE,CAAC;QAChC,MAAM,MAAM,GAAG,IAAI,gBAAgB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;QAC1D,MAAM,YAAY,GAAG;YACnB,IAAI,EAAE,gBAAgB;YACtB,IAAI,EAAE,IAAI;SACX,CAAC;QACF,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAEjE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC;YACxC,MAAM,EAAE,WAAW;YACnB,uBAAuB,EAAE,cAAc;YACvC,QAAQ,EAAE,EAAE;SACb,CAAC,CAAC;QAEH,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAC9C,aAAa,EACb,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAC/C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;QACvD,MAAM,IAAI,GAAG,IAAI,YAAY,EAAE,CAAC;QAChC,MAAM,MAAM,GAAG,IAAI,gBAAgB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;QAC1D,MAAM,YAAY,GAAG;QACnB,0BAA0B;SAC3B,CAAC;QACF,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAEjE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC;YAC3C,QAAQ,EAAE,EAAE;SACb,CAAC,CAAC;QAEH,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAC9C,gBAAgB,EAChB,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACF,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;QAC/C,MAAM,IAAI,GAAG,IAAI,YAAY,EAAE,CAAC;QAChC,MAAM,MAAM,GAAG,IAAI,gBAAgB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;QAC1D,MAAM,YAAY,GAAG;YACnB,WAAW,EAAE,GAAG;SACjB,CAAC;QACF,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAEjE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC;YACxC,KAAK,EAAE,YAAY;YACnB,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;SAC3D,CAAC,CAAC;QACH,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;QACtD,MAAM,IAAI,GAAG,IAAI,YAAY,EAAE,CAAC;QAChC,MAAM,MAAM,GAAG,IAAI,gBAAgB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;QAC1D,MAAM,MAAM,CACV,MAAM,CAAC,YAAY,CAAC;YAClB,KAAK,EAAE,YAAY;YACnB,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;SAC3D,CAAC,CACH,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;IACtB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}