{"name": "find-up-simple", "version": "1.0.1", "description": "Find a file or directory by walking up parent directories — Zero dependencies", "license": "MIT", "repository": "sindresorhus/find-up-simple", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "index.d.ts"], "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "devDependencies": {"ava": "^5.3.1", "tempy": "^3.1.0", "xo": "^0.56.0"}}