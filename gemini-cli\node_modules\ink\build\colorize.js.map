{"version": 3, "file": "colorize.js", "sourceRoot": "", "sources": ["../src/colorize.ts"], "names": [], "mappings": "AAAA,OAAO,KAA2D,MAAM,OAAO,CAAC;AAIhF,MAAM,QAAQ,GAAG,wCAAwC,CAAC;AAC1D,MAAM,SAAS,GAAG,0BAA0B,CAAC;AAE7C,MAAM,YAAY,GAAG,CAAC,KAAa,EAAgC,EAAE;IACpE,OAAO,KAAK,IAAI,KAAK,CAAC;AACvB,CAAC,CAAC;AAEF,MAAM,QAAQ,GAAG,CAChB,GAAW,EACX,KAAyB,EACzB,IAAe,EACN,EAAE;IACX,IAAI,CAAC,KAAK,EAAE,CAAC;QACZ,OAAO,GAAG,CAAC;IACZ,CAAC;IAED,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;QACzB,IAAI,IAAI,KAAK,YAAY,EAAE,CAAC;YAC3B,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1B,CAAC;QAED,MAAM,UAAU,GAAG,KAClB,KAAK,CAAC,CAAC,CAAE,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CACxC,EAAyB,CAAC;QAE1B,OAAO,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC;IAED,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QAC3B,OAAO,IAAI,KAAK,YAAY;YAC3B,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;YACvB,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5B,CAAC;IAED,IAAI,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;QACjC,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEtC,IAAI,CAAC,OAAO,EAAE,CAAC;YACd,OAAO,GAAG,CAAC;QACZ,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjC,OAAO,IAAI,KAAK,YAAY;YAC3B,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;YAC3B,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAED,IAAI,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;QAC7B,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAErC,IAAI,CAAC,OAAO,EAAE,CAAC;YACd,OAAO,GAAG,CAAC;QACZ,CAAC;QAED,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAEtC,OAAO,IAAI,KAAK,YAAY;YAC3B,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC;YACrD,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC;IAC1D,CAAC;IAED,OAAO,GAAG,CAAC;AACZ,CAAC,CAAC;AAEF,eAAe,QAAQ,CAAC"}