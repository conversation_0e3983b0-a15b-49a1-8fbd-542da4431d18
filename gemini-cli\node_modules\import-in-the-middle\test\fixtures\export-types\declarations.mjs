const o = { name5: 1, name6: 1 }
const array = [1, 1]

// Exporting declarations
export const name1 = 1; export const name2 = 1/*, … */ // also var
export const name3 = 1; export const name4 = 1/*, … */ // also var, let
export function functionName () { return 1 }
export class ClassName { getFoo () { return 1 } }
export function * generatorFunctionName () { return 1 }
export const { name5, name6: bar } = o
export const [name7, name8] = array
export async function asyncFunctionName () { return 1 }
export async function * asyncGeneratorFunctionName () { yield 1 }
export const arrowFunction = () => {
  return 1
}
export const asyncArrowFunction = async () => {
  return 1
}
