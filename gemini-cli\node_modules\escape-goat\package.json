{"name": "escape-goat", "version": "4.0.0", "description": "Escape a string for use in HTML or the inverse", "license": "MIT", "repository": "sindresorhus/escape-goat", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["escape", "unescape", "html", "entity", "entities", "escaping", "sanitize", "sanitization", "utility", "template", "attribute", "value", "interpolate", "xss", "goat", "🐐"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}}