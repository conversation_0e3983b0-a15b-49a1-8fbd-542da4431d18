{"version": 3, "file": "bfsFileSearch.js", "sourceRoot": "", "sources": ["../../../src/utils/bfsFileSearch.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAE,MAAM,aAAa,CAAC;AAClC,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAI7B,iCAAiC;AACjC,yDAAyD;AACzD,MAAM,MAAM,GAAG;IACb,8DAA8D;IAC9D,KAAK,EAAE,CAAC,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,GAAG,IAAI,CAAC;CAC7E,CAAC;AAUF;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,aAAa,CACjC,OAAe,EACf,OAA6B;IAE7B,MAAM,EACJ,QAAQ,EACR,UAAU,GAAG,EAAE,EACf,OAAO,GAAG,QAAQ,EAClB,KAAK,GAAG,KAAK,EACb,WAAW,GACZ,GAAG,OAAO,CAAC;IACZ,MAAM,UAAU,GAAa,EAAE,CAAC;IAChC,MAAM,KAAK,GAAa,CAAC,OAAO,CAAC,CAAC;IAClC,MAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAC;IAClC,IAAI,eAAe,GAAG,CAAC,CAAC;IAExB,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,eAAe,GAAG,OAAO,EAAE,CAAC;QACrD,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,EAAG,CAAC;QAClC,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5B,SAAS;QACX,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACxB,eAAe,EAAE,CAAC;QAElB,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,CAAC,KAAK,CAAC,aAAa,eAAe,IAAI,OAAO,MAAM,UAAU,EAAE,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,OAAiB,CAAC;QACtB,IAAI,CAAC;YACH,OAAO,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;QAClE,CAAC;QAAC,MAAM,CAAC;YACP,kEAAkE;YAClE,SAAS;QACX,CAAC;QAED,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YACnD,IAAI,WAAW,EAAE,mBAAmB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC/C,SAAS;YACX,CAAC;YAED,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;gBACxB,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;oBACrC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACvB,CAAC;YACH,CAAC;iBAAM,IAAI,KAAK,CAAC,MAAM,EAAE,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACrD,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,UAAU,CAAC;AACpB,CAAC"}